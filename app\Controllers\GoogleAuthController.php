<?php

namespace App\Controllers;

use CodeIgniter\Controller;
use CodeIgniter\Shield\Models\UserModel;
use League\OAuth2\Client\Provider\Google;
use League\OAuth2\Client\Provider\Exception\IdentityProviderException;
use Config\OAuth;

class Google<PERSON>uthController extends Controller
{
    protected $userModel;
    protected $oauthConfig;
    protected $googleProvider;

    public function __construct()
    {
        $this->userModel = new UserModel();
        $this->oauthConfig = new OAuth();
        
        $this->googleProvider = new Google([
            'clientId'     => $this->oauthConfig->google['client_id'],
            'clientSecret' => $this->oauthConfig->google['client_secret'],
            'redirectUri'  => $this->oauthConfig->google['redirect_uri'],
        ]);
    }

    /**
     * Redirect to Google OAuth
     */
    public function redirect()
    {
        try {
            // Check if Google OAuth is configured
            if (empty($this->oauthConfig->google['client_id']) || empty($this->oauthConfig->google['client_secret'])) {
                return redirect()->to('/login')->with('error', 'Google OAuth is not configured. Please contact administrator.');
            }

            // Generate state for security
            $state = bin2hex(random_bytes(32));
            session()->set('oauth_state', $state);
            session()->set('oauth_provider', 'google');

            // Store the intended redirect URL
            $redirectUrl = session()->getTempdata('beforeLoginUrl') ?? '/admin';
            session()->set('oauth_redirect_url', $redirectUrl);

            // Get authorization URL
            $authUrl = $this->googleProvider->getAuthorizationUrl([
                'scope' => $this->oauthConfig->google['scopes'],
                'state' => $state,
            ]);

            return redirect()->to($authUrl);

        } catch (\Exception $e) {
            log_message('error', 'Google OAuth redirect error: ' . $e->getMessage());
            return redirect()->to('/login')->with('error', 'Failed to connect to Google. Please try again.');
        }
    }

    /**
     * Handle Google OAuth callback
     */
    public function callback()
    {
        try {
            // Verify state parameter
            $state = $this->request->getGet('state');
            $sessionState = session()->get('oauth_state');
            
            if (empty($state) || $state !== $sessionState) {
                throw new \Exception('Invalid state parameter');
            }

            // Check for authorization code
            $code = $this->request->getGet('code');
            if (empty($code)) {
                $error = $this->request->getGet('error');
                $errorDescription = $this->request->getGet('error_description');
                throw new \Exception('Authorization failed: ' . ($errorDescription ?? $error ?? 'Unknown error'));
            }

            // Exchange code for access token
            $token = $this->googleProvider->getAccessToken('authorization_code', [
                'code' => $code
            ]);

            // Get user details from Google
            $googleUser = $this->googleProvider->getResourceOwner($token);
            $userData = $googleUser->toArray();

            // Process the user
            $user = $this->processOAuthUser($userData);

            if ($user) {
                // Log the user in
                auth()->login($user);

                // Clear OAuth session data
                session()->remove(['oauth_state', 'oauth_provider']);

                // Get redirect URL
                $redirectUrl = session()->get('oauth_redirect_url') ?? '/admin';
                session()->remove('oauth_redirect_url');

                return redirect()->to($redirectUrl)->with('success', 'Successfully signed in with Google!');
            } else {
                throw new \Exception('Failed to process user account');
            }

        } catch (IdentityProviderException $e) {
            log_message('error', 'Google OAuth provider error: ' . $e->getMessage());
            return redirect()->to('/login')->with('error', 'Google authentication failed. Please try again.');
        } catch (\Exception $e) {
            log_message('error', 'Google OAuth callback error: ' . $e->getMessage());
            return redirect()->to('/login')->with('error', 'Authentication failed: ' . $e->getMessage());
        }
    }

    /**
     * Process OAuth user data
     */
    protected function processOAuthUser(array $userData): ?\CodeIgniter\Shield\Entities\User
    {
        try {
            $email = $userData['email'] ?? null;
            $name = $userData['name'] ?? null;
            $googleId = $userData['id'] ?? null;

            if (empty($email) || empty($googleId)) {
                throw new \Exception('Required user data missing from Google');
            }

            // Check if user exists by email
            $existingUser = $this->userModel->findByCredentials(['email' => $email]);

            if ($existingUser) {
                // Update Google ID if not set
                $this->updateUserGoogleId($existingUser, $googleId);
                return $existingUser;
            }

            // Create new user if auto-registration is enabled
            if ($this->oauthConfig->allowAutoRegistration) {
                return $this->createOAuthUser($userData);
            } else {
                throw new \Exception('Account not found. Please register first or contact administrator.');
            }

        } catch (\Exception $e) {
            log_message('error', 'Process OAuth user error: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Create new user from OAuth data
     */
    protected function createOAuthUser(array $userData): ?\CodeIgniter\Shield\Entities\User
    {
        try {
            $email = $userData['email'];
            $name = $userData['name'] ?? '';
            $googleId = $userData['id'];

            // Generate username from email
            $username = $this->generateUsername($email);

            // Create user data
            $userEntity = new \CodeIgniter\Shield\Entities\User([
                'username' => $username,
                'email'    => $email,
                'active'   => 1,
            ]);

            // Set additional user data
            $userEntity->fill([
                'first_name' => $userData['given_name'] ?? '',
                'last_name'  => $userData['family_name'] ?? '',
                'avatar'     => $userData['picture'] ?? '',
                'google_id'  => $googleId,
                'email_verified_at' => date('Y-m-d H:i:s'),
            ]);

            // Save user
            $userId = $this->userModel->save($userEntity);
            
            if (!$userId) {
                throw new \Exception('Failed to create user account');
            }

            // Get the created user
            $user = $this->userModel->find($userId);

            // Add default role
            $user->addGroup($this->oauthConfig->defaultRole);

            // Create identity for OAuth
            $user->createEmailIdentity([
                'email'    => $email,
                'password' => null, // OAuth users don't have passwords
            ]);

            return $user;

        } catch (\Exception $e) {
            log_message('error', 'Create OAuth user error: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Update user's Google ID
     */
    protected function updateUserGoogleId($user, string $googleId): void
    {
        try {
            if (empty($user->google_id)) {
                $this->userModel->update($user->id, ['google_id' => $googleId]);
            }
        } catch (\Exception $e) {
            log_message('error', 'Update Google ID error: ' . $e->getMessage());
        }
    }

    /**
     * Generate unique username from email
     */
    protected function generateUsername(string $email): string
    {
        $baseUsername = strstr($email, '@', true);
        $baseUsername = preg_replace('/[^a-zA-Z0-9]/', '', $baseUsername);
        
        $username = $baseUsername;
        $counter = 1;

        // Ensure username is unique
        while ($this->userModel->where('username', $username)->first()) {
            $username = $baseUsername . $counter;
            $counter++;
        }

        return $username;
    }
}
