<?php

namespace App\Controllers;

use App\Models\DashboardModel;

class Admin extends BaseController
{
    protected $dashboardModel;
    
    public function __construct()
    {
        $this->dashboardModel = new DashboardModel();
    }

    public function index()
    {
        return $this->dashboard();
    }

    public function dashboard()
    {
        $data = array_merge([
            'title' => 'Dashboard',
            'stats' => $this->dashboardModel->getDashboardStats(),
            'recent_activities' => $this->dashboardModel->getRecentActivities(),
            'charts_data' => $this->dashboardModel->getChartsData()
        ], $this->getCurrentUserData());

        return view('admin/dashboard', $data);
    }

    public function students()
    {
        $data = array_merge([
            'title' => 'Students Management',
            'students' => $this->dashboardModel->getStudents()
        ], $this->getCurrentUserData());

        return view('admin/students', $data);
    }

    public function staff()
    {
        $data = array_merge([
            'title' => 'Staff Management',
            'staff' => $this->dashboardModel->getStaff()
        ], $this->getCurrentUserData());

        return view('admin/staff', $data);
    }

    public function classes()
    {
        $data = array_merge([
            'title' => 'Classes Management',
            'classes' => $this->dashboardModel->getClasses()
        ], $this->getCurrentUserData());

        return view('admin/classes', $data);
    }

    public function fees()
    {
        $data = [
            'title' => 'Fees Management',
            'fees' => $this->dashboardModel->getFees()
        ];

        return view('admin/fees', $data);
    }

    public function expenses()
    {
        $data = [
            'title' => 'Expenses Management',
            'expenses' => $this->dashboardModel->getExpenses()
        ];

        return view('admin/expenses', $data);
    }

    public function reports()
    {
        $data = [
            'title' => 'Reports',
            'reports_data' => $this->dashboardModel->getReportsData()
        ];

        return view('admin/reports', $data);
    }

    public function settings()
    {
        $data = [
            'title' => 'Settings',
            'settings' => $this->dashboardModel->getSettings()
        ];

        return view('admin/settings', $data);
    }
}
